<template>
  <div class="meta-dict-table" v-show="treeNode?.id">
    <table-page
      ref="myTableRef"
      :name="treeNode?.id"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      :operations="operations"
      @operation="handleOperation"
      :loadImmediately="false"
      :withSort="false"
      operationAuth="/base/#/meta-dict/edit"
    >
      <template #query>
        <div class="flexBetweenStart">
          <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
          <my-operation>
            <template #buttonGroup>
              <my-button type="add" @click="events.add" operationAuth="/base/#/meta-dict/edit">新建</my-button>
            </template>
          </my-operation>
        </div>
      </template>
      <template #header>
        <div class="header">
          <span>{{ treeNode?.name }}</span
          ><span>启用状态</span>
          <el-switch v-model="value1" @change="events.enableTree" :disabled="!$auth.testAuth('/base/#/meta-dict/edit')" />
        </div>
      </template>
    </table-page>

    <AddDialog ref="addRef" :treeNode="treeNode" :treeData="treeData" @save-data="loadList" :columns="columns" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch } from "vue";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as metaWordApi from "@/api/meta-word";
import AddDialog from "./add.vue";
import { getTemplateList } from "@/api/mock";
import { assign, pick, keys } from "lodash";
import { downloadFile, findNodeById } from "@/utils/common.ts";
import { computed } from "vue";
const { $app, proxy, $auth } = useCtx();
const { word } = useStore();

const props = defineProps({
  treeNode: { type: Object, default: {} },
  treeData: { type: Array },
});
//列配置
const columns = computed(() => {
  const parent = props.treeNode?.parentId && findNodeById(props.treeData, props.treeNode.parentId);
  if (props.treeNode?.name == "同义词" || parent?.name == "同义词") {
    return [
      // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
      // 文本可编辑
      {
        prop: "word",
        label: "原词",
        minWidth: 180,
      },
      {
        prop: "relationWords",
        label: "单向同义词",
        minWidth: 180,
      },
      {
        prop: "mutualRelationWords",
        label: "双向同义词",
        minWidth: 180,
      },
      {
        prop: "lastModifiedDateRender",
        label: "更新时间",
        width: 180,
      },
      { prop: "operation", label: "操作", width: 110, fixed: "right" },
    ];
  } else if (props.treeNode?.name == "纠错词" || parent?.name == "纠错词") {
    return [
      // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
      // 文本可编辑
      {
        prop: "word",
        label: "原词",
        minWidth: 180,
      },
      {
        prop: "relationWords",
        label: "纠错词",
        minWidth: 180,
      },
      {
        prop: "lastModifiedDateRender",
        label: "更新时间",
        width: 180,
      },
      { prop: "operation", label: "操作", width: 110, fixed: "right" },
    ];
  } else {
    return [
      // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
      // 文本可编辑
      {
        prop: "word",
        label: "原词",
        minWidth: 180,
      },
      {
        prop: "lastModifiedDateRender",
        label: "更新时间",
        width: 180,
      },
      { prop: "operation", label: "操作", width: 110, fixed: "right" },
    ];
  }
});
//查询面板
const query = ref<any>({
  type: "",
  keywords: "",
});
const value1 = ref(false);
const defaultForm = {
  parentId: 0,
  name: "",
  code: "",
  enabled: "",
};
const queryItems = ref<any>({
  type: {
    modelValue: "",
    type: "select",
    label: "类型",
    width: "160px",
    options: [{ value: "query", label: "原词" }],
    attrs: {
      placeholder: "请选择类型",
    },
  },
  keywords: {
    type: "input",
    label: "",
    modelValue: "",
    attrs: {
      placeholder: "请输入关键词",
    },
  },
});
//列表查询
const loadListData = (data: any) => {
  return new Promise((resolve: any) => {
    let params: any = {
      // sort: data.sort,
      page: data.page,
      size: data.size,
    };
    if (data.type == "query") {
      params.word = data.keywords;
    } else if (data.type) {
      params.relationWords = data.keywords;
    }
    if (!props.treeNode.id) {
      return;
    }
    metaWordApi.getDicPage(props.treeNode.id, params, word.area + "/").then((result) => {
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.lastModifiedDateRender = !dataC.isEmpty(x.lastModifiedDate) ? timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss") : "";
    return x;
  });
};
//操作
const operations = [
  { type: "edit", label: "编辑" },
  { type: "delete", label: "删除", btnType: "danger" },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  if (type === "edit") {
    events.edit(record);
  }
  if (type === "delete") {
    events.delete(record);
  }
};

//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  edit: (record: any) => {
    proxy.$refs.addRef?.openDialog("edit", record);
  },
  add: () => {
    proxy.$refs.addRef?.openDialog("add");
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除 ${record.word}?`,
      })
      .then(() => {
        metaWordApi.deleteDic(record.id, word.area + "/").then(() => {
          loadList();
          $app.$message.success(`删除 ${record.word} 成功`);
        });
      });
  },
  enableTree: () => {
    metaWordApi
      .editTree(props.treeNode.id, pick({ ...props.treeNode, enabled: !props.treeNode.enabled }, keys(assign({}, defaultForm))), word.area + "/")
      .then(() => {
        emit("updateTree", props.treeNode.id);
        $app.$message.success(`${!props.treeNode.enabled ? "启用" : "停用"} ${props.treeNode.name} 成功`);
      });
  },
});
const loadList = () => {
  proxy.$refs.myTableRef?.loadData();
};
//事件声明
const emit = defineEmits(["edit-data", "updateTree"]);
//接口暴露
defineExpose({
  loadList,
});
// watch监听
watch(
  () => props.treeNode,
  (val: any) => {
    queryItems.value.type.options = [
      { value: "query", label: "原词" },
      { value: val?.name, label: val?.name },
    ];
    if (!val) {
      return;
    }
    const parent = findNodeById(props.treeData, val.parentId);
    const index = columns.value.findIndex((item) => item.prop == "relationWords");
    value1.value = val.enabled;
    loadList();
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>
<style lang="scss" scoped>
.meta-dict-table {
  height: 100%;
  .header {
    span {
      margin-right: 20px;
    }
    span:first-child {
      font-weight: 550;
    }
    ::v-deep {
      .el-switch {
        margin-left: 10px;
      }
    }
  }
}
</style>
