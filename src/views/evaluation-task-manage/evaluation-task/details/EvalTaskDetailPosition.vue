<template>
  <page-wrapper route-name="experience-index::position::">
    <Position ref="positionRef" :loadDataApi="loadListData" />
  </page-wrapper>
</template>

<script lang="ts" setup>
import useCtx from "@/hooks/useCtx";
import Position from "@/views/common/position/index.vue";
import * as markApi from "@/api/eval-mark";
const { $router, proxy, $app } = useCtx();
//列表查询
const loadListData = () => {
  const routeQuery = $router.currentRoute.value.query;
  console.log("routeQuery:",routeQuery);
  const params = {
    markRecordId: routeQuery.markRecordId,
    strategyId: routeQuery.strategyId,
    docId: routeQuery.docId,
    docUrl: routeQuery.docUrl,
    recall: false,
    trace: true,
    chat: false,
  };
  console.log("experience position:",params);

  return markApi.getMarkReocordById(params.markRecordId, params);
};
const getTraceinfo = () => {
  proxy.$refs.positionRef.getTraceinfo();
};
//接口暴露
defineExpose({ getTraceinfo });
</script>
<style lang="scss" scoped></style>